/*your custom css goes here*/

/* <PERSON><PERSON><PERSON><PERSON> 品牌配色方案 */
:root {
    /* 主要颜色 - 基于<PERSON><PERSON>蓝色 */
    --primary: #3B82C4 !important;
    --hov-primary: #2E86AB !important;
    --soft-primary: rgba(59, 130, 196, 0.15) !important;

    /* 辅助颜色 */
    --secondary: #64748b !important;
    --soft-secondary: rgba(100, 116, 139, 0.15) !important;

    /* 成功色 */
    --success: #059669 !important;
    --soft-success: rgba(5, 150, 105, 0.15) !important;

    /* 警告色 - 使用E.Leclerc橙色 */
    --warning: #F77B0B !important;
    --soft-warning: rgba(247, 123, 11, 0.15) !important;

    /* 危险色 */
    --danger: #dc2626 !important;
    --soft-danger: rgba(220, 38, 38, 0.15) !important;
}

/* E.<PERSON>clerc橙色强调元素 */
.leclerc-accent {
    color: #F77B0B !important;
}

.leclerc-accent-bg {
    background-color: #F77B0B !important;
    color: white !important;
}

/* 按钮样式优化 */
.btn-primary {
    background-color: var(--primary) !important;
    border-color: var(--primary) !important;
    transition: all 0.3s ease !important;
}

.btn-primary:hover {
    background-color: var(--hov-primary) !important;
    border-color: var(--hov-primary) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 196, 0.3) !important;
}

/* 橙色按钮（特殊促销等） */
.btn-leclerc {
    background-color: #F77B0B !important;
    border-color: #F77B0B !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.btn-leclerc:hover {
    background-color: #E56F0E !important;
    border-color: #E56F0E !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(247, 123, 11, 0.3) !important;
}

/* 链接样式优化 */
a {
    color: var(--primary) !important;
    transition: color 0.3s ease !important;
}

a:hover {
    color: var(--hov-primary) !important;
}

/* 价格标签 - 使用橙色强调 */
.price, .product-price {
    color: #F77B0B !important;
    font-weight: 600 !important;
}

/* 搜索框优化 */
.form-control:focus {
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 196, 0.25) !important;
}

/* 导航栏优化 */
.navbar-brand {
    transition: transform 0.3s ease !important;
}

.navbar-brand:hover {
    transform: scale(1.05) !important;
}

/* 卡片阴影优化 */
.card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transition: box-shadow 0.3s ease !important;
}

.card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
}

/* 徽章优化 */
.badge-primary {
    background-color: var(--primary) !important;
}

.badge-soft-primary {
    background-color: var(--soft-primary) !important;
    color: var(--primary) !important;
}

/* 特殊促销徽章 - 橙色 */
.badge-sale, .badge-promotion {
    background-color: #F77B0B !important;
    color: white !important;
}

/* 前端登录注册页面背景优化 */
.gry-bg {
    background-color: #f8f9fa !important;
    position: relative !important;
    min-height: 100vh !important;
}

/* 当有背景图时的样式 */
.gry-bg[style*="background-image"] {
    background-attachment: fixed !important;
    background-blend-mode: overlay !important;
    background-color: rgba(248, 249, 250, 0.3) !important;
}

/* 登录注册卡片样式优化 */
.profile .card {
    backdrop-filter: blur(15px) !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
    border: none !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
    border-radius: 15px !important;
    transition: all 0.3s ease !important;
}

.profile .card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
}

/* 表单输入框优化 */
.profile .form-control {
    border-radius: 8px !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    padding: 12px 15px !important;
    transition: all 0.3s ease !important;
}

.profile .form-control:focus {
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 196, 0.25) !important;
    transform: translateY(-1px) !important;
}

/* 按钮优化 */
.profile .btn {
    border-radius: 8px !important;
    padding: 12px 20px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

/* YouTube 视频播放器样式 - 固定在视窗左下角 */
.youtube-player-fixed {
    position: fixed !important;
    bottom: 20px !important;
    left: 20px !important;
    width: 350px !important;
    max-width: calc(100vw - 40px) !important;
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
    z-index: 9999 !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    border: 2px solid var(--primary) !important;
    /* 确保始终在视窗内显示 */
    max-height: calc(100vh - 40px) !important;
    /* 防止被其他元素遮挡 */
    pointer-events: auto !important;
    /* 确保在所有页面上都能正确显示 */
    visibility: visible !important;
    opacity: 1 !important;
}

.youtube-player-fixed.minimized {
    display: none !important;
}

.youtube-player-header {
    background: linear-gradient(135deg, var(--primary), var(--hov-primary)) !important;
    color: white !important;
    padding: 10px 15px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    cursor: grab !important;
    user-select: none !important;
}

.youtube-player-header:active {
    cursor: grabbing !important;
}

.youtube-player-title {
    margin: 0 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    flex: 1 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.youtube-player-controls {
    display: flex !important;
    gap: 5px !important;
}

.btn-control {
    background: rgba(255, 255, 255, 0.2) !important;
    border: none !important;
    color: white !important;
    width: 28px !important;
    height: 28px !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    font-size: 14px !important;
}

.btn-control:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: scale(1.1) !important;
}

.youtube-player-content {
    padding: 0 !important;
}

.youtube-embed-container {
    position: relative !important;
    width: 100% !important;
    height: 0 !important;
    padding-bottom: 56.25% !important; /* 16:9 宽高比 */
}

.youtube-embed-container iframe {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border: none !important;
}

/* 最小化按钮 - 固定在视窗左下角 */
.youtube-minimized-button {
    position: fixed !important;
    bottom: 20px !important;
    left: 20px !important;
    background: linear-gradient(135deg, var(--primary), var(--hov-primary)) !important;
    color: white !important;
    padding: 12px 20px !important;
    border-radius: 25px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
    cursor: pointer !important;
    z-index: 9998 !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    max-width: 250px !important;
    /* 确保在视窗内显示 */
    max-height: 60px !important;
}

.youtube-minimized-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3) !important;
}

.youtube-minimized-button i {
    font-size: 18px !important;
    color: #FF0000 !important;
}

.youtube-minimized-button span {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .youtube-player-fixed {
        width: 280px !important;
        bottom: 10px !important;
        left: 10px !important;
    }

    .youtube-minimized-button {
        bottom: 10px !important;
        left: 10px !important;
        padding: 10px 15px !important;
        max-width: 200px !important;
    }

    .youtube-player-title {
        font-size: 12px !important;
    }
}

@media (max-width: 480px) {
    .youtube-player-fixed {
        width: calc(100vw - 20px) !important;
        left: 10px !important;
        right: 10px !important;
    }

    .youtube-minimized-button {
        max-width: 150px !important;
    }

    .youtube-minimized-button span {
        display: none !important;
    }
}

/* 自定义视频播放器样式 */
#custom-video-player {
    transition: all 0.3s ease !important;
}

#custom-video-player:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4) !important;
}

#video-player-header {
    transition: background 0.3s ease !important;
}

#video-player-header:hover {
    background: linear-gradient(135deg, #2E86AB, #1d4ed8) !important;
}

#homepage-video {
    transition: all 0.3s ease !important;
}

#video-minimized-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3) !important;
}

/* 视频播放器响应式设计 */
@media (max-width: 768px) {
    #custom-video-player {
        width: 280px !important;
        bottom: 10px !important;
        left: 10px !important;
    }

    #video-minimized-btn {
        bottom: 10px !important;
        left: 10px !important;
        padding: 10px 15px !important;
        max-width: 200px !important;
    }
}

@media (max-width: 480px) {
    #custom-video-player {
        width: calc(100vw - 20px) !important;
        left: 10px !important;
        right: 10px !important;
    }

    #video-minimized-btn {
        max-width: 150px !important;
    }

    #video-minimized-btn span {
        display: none !important;
    }
}